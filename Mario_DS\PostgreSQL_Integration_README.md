# PostgreSQL Integration for Mario Game Score System

## Overview

This implementation replaces the text file-based score saving system with a PostgreSQL database solution. The system maintains the same Observer pattern architecture while providing more robust data persistence and querying capabilities.

## Files Created/Modified

### New Files:

1. **DatabaseConfig.java** - Handles database connection and initialization
2. **PostgreSQLScoreSaver.java** - PostgreSQL implementation of GameObserver for saving scores
3. **ScoreRepository.java** - Data access layer for reading scores from database
4. **DatabaseTest.java** - Test class to verify database connectivity and operations
5. **pom.xml** - Maven configuration with PostgreSQL dependencies

### Modified Files:

1. **GameEngine.java** - Updated to use PostgreSQLScoreSaver instead of ScoreSaver
2. **.classpath** - Added PostgreSQL JDBC driver dependency

## Database Schema

The system creates a `mario_scores` table with the following structure:

```sql
CREATE TABLE IF NOT EXISTS mario_scores (
    id SERIAL PRIMARY KEY,
    mario_score INTEGER NOT NULL,
    mario2_score INTEGER NOT NULL,
    total_score INTEGER NOT NULL,
    game_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

## Features

### PostgreSQLScoreSaver

- Implements the same `GameObserver` interface as the original ScoreSaver
- Automatically saves scores when game ends (GAME_OVER or MISSION_PASSED)
- Includes fallback to file-based saving if database connection fails
- Stores both individual Mario scores and calculated total score
- Records timestamp of each game session

### ScoreRepository

- `getAllScores()` - Retrieves all scores ordered by date (newest first)
- `getTopScores(limit)` - Gets top scores ordered by total score
- `getHighestScore()` - Returns the highest total score achieved
- `deleteAllScores()` - Removes all scores from database
- `printAllScores()` - Displays all scores in formatted output

### DatabaseConfig

- Reads database URL from `.env` file
- Automatically initializes database connection and creates tables
- Handles connection management and cleanup

## Configuration

The database connection is configured via the `.env` file:

```
DATABASE_URL = "postgresql://postgres:<EMAIL>:59568/railway"
```

## Dependencies

- PostgreSQL JDBC Driver (postgresql-42.7.3.jar) - Downloaded to `lib/` directory
- Java 11+ (for text blocks and modern Java features)

## Testing

### Running the Database Test

1. Ensure Java is installed and in your PATH
2. Compile the code:
   ```bash
   cd Mario_DS
   javac -cp "lib/postgresql-42.7.3.jar" -d bin src/manager/ScoreManager/*.java src/manager/*.java
   ```
3. Run the test:
   ```bash
   java -cp "lib/postgresql-42.7.3.jar;bin" manager.ScoreManager.DatabaseTest
   ```

### Expected Test Output

```
=== Testing PostgreSQL Connection ===
Testing database connection...
Database initialized successfully
Testing score insertion...
Test score inserted successfully!
Mario: 1500 - Mario2: 2300 - Total: 3800 - Date: [timestamp]
Testing score retrieval...
Found 1 scores in database
=== ALL SCORES FROM DATABASE ===
1. Mario: 1500  - Mario2: 2300  - Date: [timestamp] (Total: 3800)
================================
Highest score: 3800
Top 5 scores: 1 found
=== Database test completed successfully! ===
```

## Integration Points

### GameEngine Integration

The main change in `GameEngine.java`:

```java
// Old implementation
ScoreSaver scoreSaver = new ScoreSaver("./src/media/score/score.txt");

// New implementation
PostgreSQLScoreSaver scoreSaver = new PostgreSQLScoreSaver();
```

### Backward Compatibility

- The original `ScoreSaver` class remains unchanged
- `PostgreSQLScoreSaver` includes fallback to file-based saving if database fails
- Same Observer pattern interface ensures seamless integration

## Advantages Over File-Based System

1. **Concurrent Access** - Multiple game instances can safely write to database
2. **Data Integrity** - ACID properties ensure consistent data
3. **Query Capabilities** - Easy to get top scores, statistics, etc.
4. **Scalability** - Can handle large numbers of score records efficiently
5. **Backup/Recovery** - Database-level backup and recovery options
6. **Data Validation** - Database constraints ensure data quality

## Error Handling

- Database connection errors are logged and fallback to file saving occurs
- SQL exceptions are caught and logged with detailed error messages
- Connection pooling (if implemented) would handle connection management
- Graceful degradation ensures game continues even if database is unavailable

## Future Enhancements

1. **Player Names** - Add player name tracking
2. **Game Statistics** - Track additional metrics (time played, level reached, etc.)
3. **Leaderboards** - Web interface for viewing top scores
4. **Connection Pooling** - Use HikariCP for better connection management
5. **Data Migration** - Tool to import existing text file scores to database
