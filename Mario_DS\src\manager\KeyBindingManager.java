package manager;

import java.awt.event.ActionEvent;
import java.awt.event.KeyEvent;

import javax.swing.AbstractAction;
import javax.swing.ActionMap;
import javax.swing.InputMap;
import javax.swing.JComponent;
import javax.swing.JFrame;
import javax.swing.KeyStroke;

public class KeyBindingManager {

	private final GameEngine engine;

	public KeyBindingManager(GameEngine engine, JFrame frame) {
		this.engine = engine;
		int condition = JComponent.WHEN_IN_FOCUSED_WINDOW;
		InputMap inputMap = frame.getRootPane().getInputMap(condition);
		ActionMap actionMap = frame.getRootPane().getActionMap();

		bindKeys(inputMap, actionMap);
	}

	private void bindKeys(InputMap inputMap, ActionMap actionMap) {
		// Menu navigation keys (S and DOWN for down navigation)
		bindKey(inputMap, actionMap, KeyEvent.VK_S, "MOVE_DOWN", ButtonAction.GO_DOWN, null);
		bindKey(inputMap, actionMap, KeyEvent.VK_DOWN, "ARROW_DOWN", ButtonAction.GO_DOWN, null);

		// Dual purpose keys (W and UP for menu up navigation AND player jumps)
		bindKey(inputMap, actionMap, KeyEvent.VK_W, "PLAYER_MOVE_JUMP", ButtonAction.GO_UP, ButtonAction.M_JUMP);
		bindKey(inputMap, actionMap, KeyEvent.VK_UP, "SECOND_PLAYER_JUMP", ButtonAction.GO_UP, ButtonAction.M2_JUMP);

		// Player 1 controls (WASD)
		bindKey(inputMap, actionMap, KeyEvent.VK_D, "PLAYER_MOVE_RIGHT", null, ButtonAction.M_RIGHT);
		bindKey(inputMap, actionMap, KeyEvent.VK_A, "PLAYER_MOVE_LEFT", null, ButtonAction.M_LEFT);

		// General controls
		bindKey(inputMap, actionMap, KeyEvent.VK_ENTER, "SELECT", ButtonAction.SELECT, null);
		bindKey(inputMap, actionMap, KeyEvent.VK_ESCAPE, "PAUSE_RESUME", ButtonAction.PAUSE_RESUME,
				ButtonAction.GO_TO_START_SCREEN);
		bindKey(inputMap, actionMap, KeyEvent.VK_SPACE, "PLAYER_FIRE", null, ButtonAction.M_FIRE);

		// Player 2 controls (Arrow keys)
		bindKey(inputMap, actionMap, KeyEvent.VK_RIGHT, "SECOND_PLAYER_RIGHT", null, ButtonAction.M2_RIGHT);
		bindKey(inputMap, actionMap, KeyEvent.VK_LEFT, "SECOND_PLAYER_LEFT", null, ButtonAction.M2_LEFT);
		bindKey(inputMap, actionMap, KeyEvent.VK_P, "SECOND_PLAYER_FIRE", null, ButtonAction.M2_FIRE);
	}

	private void bindKey(InputMap inputMap, ActionMap actionMap, int keyEvent, String actionName,
			ButtonAction menuAction, ButtonAction gameAction) {
		inputMap.put(KeyStroke.getKeyStroke(keyEvent, 0, false), actionName + "_Press");
		inputMap.put(KeyStroke.getKeyStroke(keyEvent, 0, true), actionName + "_Release");

		actionMap.put(actionName + "_Press", new AbstractAction() {
			@Override
			public void actionPerformed(ActionEvent e) {
				GameStatus status = engine.getGameStatus();

				if ((status == GameStatus.START_SCREEN || status == GameStatus.MAP_SELECTION
						|| status == GameStatus.MULTIPLAYER_MODE_SELECTION) && menuAction != null) {
					engine.receiveInput(menuAction);
				} else if (gameAction != null) {
					if (status == GameStatus.RUNNING) {
						// Route input through MultiplayerManager for proper mode handling
						MultiplayerManager multiplayerManager = engine.getMultiplayerManager();
						if (actionName.startsWith("PLAYER")) {
							multiplayerManager.handleLocalInput(gameAction, true);
						} else if (actionName.startsWith("SECOND_PLAYER")) {
							multiplayerManager.handleLocalInput(gameAction, false);
						} else {
							// Handle other game actions that aren't routed through multiplayer manager
							engine.receiveInput(gameAction);
						}
					} else {
						engine.receiveInput(gameAction);
					}
				}
			}
		});

		actionMap.put(actionName + "_Release", new AbstractAction() {
			@Override
			public void actionPerformed(ActionEvent e) {
				GameStatus status = engine.getGameStatus();
				if (status == GameStatus.RUNNING) {
					MultiplayerManager multiplayerManager = engine.getMultiplayerManager();
					if (keyEvent == KeyEvent.VK_D || keyEvent == KeyEvent.VK_A) {
						multiplayerManager.handleLocalInput(ButtonAction.ACTION_COMPLETED, true);
					} else if (keyEvent == KeyEvent.VK_RIGHT || keyEvent == KeyEvent.VK_LEFT) {
						multiplayerManager.handleLocalInput(ButtonAction.ACTION_COMPLETED, false);
					}
				}
			}
		});
	}
}
