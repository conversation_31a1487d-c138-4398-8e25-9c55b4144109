package manager;

import java.awt.event.ActionEvent;
import java.awt.event.KeyEvent;

import javax.swing.AbstractAction;
import javax.swing.ActionMap;
import javax.swing.InputMap;
import javax.swing.JComponent;
import javax.swing.JFrame;
import javax.swing.KeyStroke;

public class KeyBindingManager {

	private final GameEngine engine;

	public KeyBindingManager(GameEngine engine, JFrame frame) {
		this.engine = engine;
		int condition = JComponent.WHEN_IN_FOCUSED_WINDOW;
		InputMap inputMap = frame.getRootPane().getInputMap(condition);
		ActionMap actionMap = frame.getRootPane().getActionMap();

		bindKeys(inputMap, actionMap);
	}

	private void bindKeys(InputMap inputMap, ActionMap actionMap) {
		bindKey(inputMap, actionMap, KeyEvent.VK_S, "MOVE_DOWN", ButtonAction.GO_DOWN, null);
		bind<PERSON>ey(inputMap, actionMap, KeyEvent.VK_W, "PLAYER_MOVE_JUMP", ButtonAction.GO_UP, ButtonAction.M_JUMP);
		bindKey(inputMap, actionMap, KeyEvent.VK_D, "PLAYER_MOVE_RIGHT", null, ButtonAction.M_RIGHT);
		bindKey(inputMap, actionMap, KeyEvent.VK_A, "PLAYER_MOVE_LEFT", null, ButtonAction.M_LEFT);

		bindKey(inputMap, actionMap, KeyEvent.VK_ENTER, "SELECT", ButtonAction.SELECT, null);
		bindKey(inputMap, actionMap, KeyEvent.VK_ESCAPE, "PAUSE_RESUME", ButtonAction.PAUSE_RESUME, ButtonAction.GO_TO_START_SCREEN);
		bindKey(inputMap, actionMap, KeyEvent.VK_SPACE, "PLAYER_FIRE", null, ButtonAction.M_FIRE);

		bindKey(inputMap, actionMap, KeyEvent.VK_UP, "SECOND_PLAYER_JUMP", null, ButtonAction.M2_JUMP);
		bindKey(inputMap, actionMap, KeyEvent.VK_RIGHT, "SECOND_PLAYER_RIGHT", null, ButtonAction.M2_RIGHT);
		bindKey(inputMap, actionMap, KeyEvent.VK_LEFT, "SECOND_PLAYER_LEFT", null, ButtonAction.M2_LEFT);
		bindKey(inputMap, actionMap, KeyEvent.VK_P, "SECOND_PLAYER_FIRE", null, ButtonAction.M2_FIRE);
	}

	private void bindKey(InputMap inputMap, ActionMap actionMap, int keyEvent, String actionName,
			ButtonAction menuAction, ButtonAction gameAction) {
		inputMap.put(KeyStroke.getKeyStroke(keyEvent, 0, false), actionName + "_Press");
		inputMap.put(KeyStroke.getKeyStroke(keyEvent, 0, true), actionName + "_Release");

		actionMap.put(actionName + "_Press", new AbstractAction() {
			@Override
			public void actionPerformed(ActionEvent e) {
				GameStatus status = engine.getGameStatus();

				if ((status == GameStatus.START_SCREEN || status == GameStatus.MAP_SELECTION) && menuAction != null) {
					engine.receiveInput(menuAction);
				} else if (gameAction != null) {
					if (status == GameStatus.RUNNING) {
						if (actionName.startsWith("PLAYER")) {
							engine.receiveInputMario(gameAction);
						} else if (actionName.startsWith("SECOND_PLAYER")) {
							engine.receiveInputMario2(gameAction);
						}
					} else {
						engine.receiveInput(gameAction);
					}
				}
			}
		});

		actionMap.put(actionName + "_Release", new AbstractAction() {
			@Override
			public void actionPerformed(ActionEvent e) {
				if (keyEvent == KeyEvent.VK_D || keyEvent == KeyEvent.VK_A) {
					engine.receiveInputMario(ButtonAction.ACTION_COMPLETED);
				} else if (keyEvent == KeyEvent.VK_RIGHT || keyEvent == KeyEvent.VK_LEFT) {
					engine.receiveInputMario2(ButtonAction.ACTION_COMPLETED);
				}
			}
		});
	}
}
