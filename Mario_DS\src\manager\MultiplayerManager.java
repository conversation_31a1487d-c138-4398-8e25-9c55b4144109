package manager;

import manager.network.NetworkManager;
import model.hero.Mario;

/**
 * Manages multiplayer functionality across different game modes.
 * Coordinates input handling, player management, and network communication.
 */
public class MultiplayerManager {

	private GameMode currentMode;
	private NetworkManager networkManager;
	private GameEngine gameEngine;

	// Player management
	private boolean player1Active;
	private boolean player2Active;
	private String localPlayerId;
	private String remotePlayerId;

	public MultiplayerManager(GameEngine gameEngine) {
		this.gameEngine = gameEngine;
		this.currentMode = GameMode.LOCAL_SINGLE_PLAYER;
		this.player1Active = true;
		this.player2Active = false;
		this.localPlayerId = "mario";
		this.remotePlayerId = "mario2";
	}

	/**
	 * Set the current game mode and configure the multiplayer system accordingly
	 * 
	 * @param mode The new game mode to set
	 */
	public void setGameMode(GameMode mode) {
		this.currentMode = mode;
		configurePlayersForMode();

		if (mode.isNetworkMode() && networkManager == null) {
			networkManager = new NetworkManager(this);
		}
	}

	/**
	 * Configure which players are active based on the current game mode
	 */
	private void configurePlayersForMode() {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				player1Active = true;
				player2Active = false;
				break;

			case LOCAL_MULTIPLAYER:
				player1Active = true;
				player2Active = true;
				break;

			case NETWORK_HOST:
				player1Active = true;
				player2Active = true; // Remote player will be controlled via network
				localPlayerId = "mario";
				remotePlayerId = "mario2";
				break;

			case NETWORK_CLIENT:
				player1Active = false; // Host controls mario
				player2Active = true; // Client controls mario2
				localPlayerId = "mario2";
				remotePlayerId = "mario";
				break;
		}
	}

	/**
	 * Handle input based on the current game mode and player assignment
	 * 
	 * @param action   The button action to process
	 * @param playerId The player ID ("mario" or "mario2")
	 */
	public void handleInput(ButtonAction action, String playerId) {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				if (playerId.equals("mario")) {
					gameEngine.receiveInputMario(action);
				}
				break;

			case LOCAL_MULTIPLAYER:
				if (playerId.equals("mario")) {
					gameEngine.receiveInputMario(action);
				} else if (playerId.equals("mario2")) {
					gameEngine.receiveInputMario2(action);
				}
				break;

			case NETWORK_HOST:
				if (playerId.equals(localPlayerId)) {
					gameEngine.receiveInputMario(action);
				} else if (networkManager != null) {
					// Handle remote player input received from network
					gameEngine.receiveInputMario2(action);
				}
				break;

			case NETWORK_CLIENT:
				if (playerId.equals(localPlayerId)) {
					// Send input to host via network
					if (networkManager != null) {
						networkManager.sendPlayerInput(action, localPlayerId);
					}
				}
				break;
		}
	}

	/**
	 * Handle input from local keyboard/controls
	 * 
	 * @param action    The button action from local input
	 * @param isPlayer1 True if input is from player 1 controls, false for player 2
	 */
	public void handleLocalInput(ButtonAction action, boolean isPlayer1) {
		String playerId = isPlayer1 ? "mario" : "mario2";

		// Only process input if the player is locally controlled
		if ((isPlayer1 && isPlayerLocallyControlled("mario")) ||
				(!isPlayer1 && isPlayerLocallyControlled("mario2"))) {
			handleInput(action, playerId);
		}
	}

	/**
	 * Check if a player is controlled locally (not via network)
	 * 
	 * @param playerId The player ID to check
	 * @return true if the player is controlled locally
	 */
	public boolean isPlayerLocallyControlled(String playerId) {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				return playerId.equals("mario");

			case LOCAL_MULTIPLAYER:
				return true; // Both players are local

			case NETWORK_HOST:
				return playerId.equals(localPlayerId);

			case NETWORK_CLIENT:
				return playerId.equals(localPlayerId);

			default:
				return false;
		}
	}

	/**
	 * Check if a player should be active/visible in the current mode
	 * 
	 * @param playerId The player ID to check
	 * @return true if the player should be active
	 */
	public boolean isPlayerActive(String playerId) {
		if (playerId.equals("mario")) {
			return player1Active;
		} else if (playerId.equals("mario2")) {
			return player2Active;
		}
		return false;
	}

	/**
	 * Start hosting a network game
	 * 
	 * @param port The port to host on
	 * @return true if hosting started successfully
	 */
	public boolean startHosting(int port) {
		if (networkManager != null) {
			return networkManager.startServer(port);
		}
		return false;
	}

	/**
	 * Connect to a network host
	 * 
	 * @param hostIP The IP address of the host
	 * @param port   The port to connect to
	 * @return true if connection was successful
	 */
	public boolean connectToHost(String hostIP, int port) {
		if (networkManager != null) {
			return networkManager.connectToServer(hostIP, port);
		}
		return false;
	}

	/**
	 * Process a game state update received from the network
	 * 
	 * @param gameState The game state data
	 */
	public void processNetworkGameState(Object gameState) {
		if (currentMode == GameMode.NETWORK_CLIENT && networkManager != null) {
			// Apply the authoritative game state from the host
			networkManager.applyGameState(gameState);
		}
	}

	/**
	 * Get the current game mode
	 * 
	 * @return The current GameMode
	 */
	public GameMode getCurrentMode() {
		return currentMode;
	}

	/**
	 * Get the network manager instance
	 * 
	 * @return The NetworkManager, or null if not in network mode
	 */
	public NetworkManager getNetworkManager() {
		return networkManager;
	}

	/**
	 * Cleanup resources when shutting down multiplayer
	 */
	public void cleanup() {
		if (networkManager != null) {
			networkManager.cleanup();
			networkManager = null;
		}
	}

	/**
	 * Get a display-friendly name for the current game mode
	 * 
	 * @return String representation of the current mode
	 */
	public String getCurrentModeDisplayName() {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				return "Single Player";
			case LOCAL_MULTIPLAYER:
				return "Local Multiplayer";
			case NETWORK_HOST:
				return "Network Host";
			case NETWORK_CLIENT:
				return "Network Client";
			default:
				return "Unknown";
		}
	}
}
