package manager;

import manager.network.NetworkManager;
import model.hero.Mario;

/**
 * Manages multiplayer functionality across different game modes.
 * Coordinates input handling, player management, and network communication.
 */
public class MultiplayerManager {

	private GameMode currentMode;
	private NetworkManager networkManager;
	private GameEngine gameEngine;

	// Player management
	private boolean player1Active;
	private boolean player2Active;
	private String localPlayerId;
	private String remotePlayerId;

	// Network synchronization
	private int frameCounter = 0;
	private long lastBroadcastTime = 0;
	private static final long BROADCAST_INTERVAL_MS = 50; // Broadcast every 50ms (20 FPS)

	public MultiplayerManager(GameEngine gameEngine) {
		this.gameEngine = gameEngine;
		this.currentMode = GameMode.LOCAL_SINGLE_PLAYER;
		this.player1Active = true;
		this.player2Active = false;
		this.localPlayerId = "mario";
		this.remotePlayerId = "mario2";
	}

	/**
	 * Set the current game mode and configure the multiplayer system accordingly
	 *
	 * @param mode The new game mode to set
	 */
	public void setGameMode(GameMode mode) {
		this.currentMode = mode;
		configurePlayersForMode();

		if (mode.isNetworkMode() && networkManager == null) {
			networkManager = new NetworkManager(this);
		}
	}

	/**
	 * Configure which players are active based on the current game mode
	 */
	private void configurePlayersForMode() {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				player1Active = true;
				player2Active = false;
				break;

			case LOCAL_MULTIPLAYER:
				player1Active = true;
				player2Active = true;
				break;

			case NETWORK_HOST:
				player1Active = true;
				player2Active = true; // Remote player will be controlled via network
				localPlayerId = "mario";
				remotePlayerId = "mario2";
				break;

			case NETWORK_CLIENT:
				player1Active = false; // Host controls mario
				player2Active = true; // Client controls mario2
				localPlayerId = "mario2";
				remotePlayerId = "mario";
				break;
		}
	}

	/**
	 * Handle input based on the current game mode and player assignment
	 *
	 * @param action   The button action to process
	 * @param playerId The player ID ("mario" or "mario2")
	 */
	public void handleInput(ButtonAction action, String playerId) {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				if (playerId.equals("mario")) {
					gameEngine.receiveInputMario(action);
				}
				break;

			case LOCAL_MULTIPLAYER:
				if (playerId.equals("mario")) {
					gameEngine.receiveInputMario(action);
				} else if (playerId.equals("mario2")) {
					gameEngine.receiveInputMario2(action);
				}
				break;

			case NETWORK_HOST:
				if (playerId.equals(localPlayerId)) {
					gameEngine.receiveInputMario(action);
				} else if (networkManager != null) {
					// Handle remote player input received from network
					gameEngine.receiveInputMario2(action);
				}
				break;

			case NETWORK_CLIENT:
				if (playerId.equals(localPlayerId)) {
					// Send input to host via network
					if (networkManager != null) {
						networkManager.sendPlayerInput(action, localPlayerId);
					}
				}
				break;
		}
	}

	/**
	 * Handle input from local keyboard/controls
	 *
	 * @param action    The button action from local input
	 * @param isPlayer1 True if input is from player 1 controls, false for player 2
	 */
	public void handleLocalInput(ButtonAction action, boolean isPlayer1) {
		String playerId = isPlayer1 ? "mario" : "mario2";

		// Only process input if the player is locally controlled
		if ((isPlayer1 && isPlayerLocallyControlled("mario")) ||
				(!isPlayer1 && isPlayerLocallyControlled("mario2"))) {
			handleInput(action, playerId);
		}
	}

	/**
	 * Check if a player is controlled locally (not via network)
	 *
	 * @param playerId The player ID to check
	 * @return true if the player is controlled locally
	 */
	public boolean isPlayerLocallyControlled(String playerId) {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				return playerId.equals("mario");

			case LOCAL_MULTIPLAYER:
				return true; // Both players are local

			case NETWORK_HOST:
				return playerId.equals(localPlayerId);

			case NETWORK_CLIENT:
				return playerId.equals(localPlayerId);

			default:
				return false;
		}
	}

	/**
	 * Check if a player should be active/visible in the current mode
	 *
	 * @param playerId The player ID to check
	 * @return true if the player should be active
	 */
	public boolean isPlayerActive(String playerId) {
		if (playerId.equals("mario")) {
			return player1Active;
		} else if (playerId.equals("mario2")) {
			return player2Active;
		}
		return false;
	}

	/**
	 * Start hosting a network game
	 *
	 * @param port The port to host on
	 * @return true if hosting started successfully
	 */
	public boolean startHosting(int port) {
		if (networkManager != null) {
			return networkManager.startServer(port);
		}
		return false;
	}

	/**
	 * Connect to a network host
	 *
	 * @param hostIP The IP address of the host
	 * @param port   The port to connect to
	 * @return true if connection was successful
	 */
	public boolean connectToHost(String hostIP, int port) {
		if (networkManager != null) {
			return networkManager.connectToServer(hostIP, port);
		}
		return false;
	}

	/**
	 * Process a game state update received from the network
	 *
	 * @param gameState The game state data
	 */
	public void processNetworkGameState(Object gameState) {
		if (currentMode == GameMode.NETWORK_CLIENT && networkManager != null) {
			// Apply the authoritative game state from the host
			networkManager.applyGameState(gameState);
		}
	}

	/**
	 * Broadcast current game state to connected clients (host only)
	 *
	 * @param gameEngine The game engine to extract state from
	 */
	public void broadcastGameState(GameEngine gameEngine) {
		if (currentMode == GameMode.NETWORK_HOST && networkManager != null && networkManager.isConnected()) {
			long currentTime = System.currentTimeMillis();

			// Only broadcast at specified intervals to avoid overwhelming the network
			if (currentTime - lastBroadcastTime >= BROADCAST_INTERVAL_MS) {
				// Create serializable game state from current game engine state
				manager.network.SerializableGameState gameState = manager.network.GameStateSerializer
						.serializeGameState(gameEngine, frameCounter);

				// Send to connected clients
				networkManager.sendGameStateUpdate(gameState, frameCounter);
				frameCounter++;
				lastBroadcastTime = currentTime;
			}
		}
	}

	/**
	 * Process any pending network updates (client only)
	 */
	public void processNetworkUpdates() {
		if (currentMode == GameMode.NETWORK_CLIENT && networkManager != null) {
			// Network updates are processed automatically in NetworkManager's message
			// handling
			// This method can be used for additional client-side processing if needed
		}
	}

	/**
	 * Apply received game state from network (client only)
	 *
	 * @param gameState The serializable game state to apply
	 */
	public void applyReceivedGameState(manager.network.SerializableGameState gameState) {
		if (currentMode != GameMode.NETWORK_CLIENT) {
			return; // Only clients should apply received game state
		}

		// Apply the authoritative game state from the host
		// This will be implemented in the next step with actual game object updates
		System.out.println("Client applying game state from host: frame " + gameState.getFrameNumber());

		// TODO: Apply Mario positions, enemy states, prize states, etc.
		// For now, we'll implement basic Mario position synchronization
		applyMarioStates(gameState);
	}

	/**
	 * Apply Mario character states from network game state
	 */
	private void applyMarioStates(manager.network.SerializableGameState gameState) {
		// Get current Mario objects from the game engine
		manager.MapManager mapManager = gameEngine.getMapManager();

		// Apply Mario 1 state (host player)
		if (gameState.getMario() != null && mapManager.getMario("mario") != null) {
			manager.network.SerializableMario marioState = gameState.getMario();
			model.hero.Mario mario = mapManager.getMario("mario");

			// Update position and movement state
			mario.setX(marioState.getX());
			mario.setY(marioState.getY());
			mario.setVelX(marioState.getVelX());
			mario.setVelY(marioState.getVelY());
			mario.setJumping(marioState.isJumping());
			mario.setFalling(marioState.isFalling());

			// Update game state (lives, coins, points)
			mario.setRemainingLives(marioState.getRemainingLives());
			mario.setCoins(marioState.getCoins());
			mario.setPoints(marioState.getPoints());
		}

		// Apply Mario 2 state (client player) - but don't override local input
		if (gameState.getMario2() != null && mapManager.getMario("mario2") != null) {
			manager.network.SerializableMario mario2State = gameState.getMario2();
			model.hero.Mario mario2 = mapManager.getMario("mario2");

			// For client's own character, only sync non-input related state
			// Position and movement are controlled locally, but sync game state
			mario2.setRemainingLives(mario2State.getRemainingLives());
			mario2.setCoins(mario2State.getCoins());
			mario2.setPoints(mario2State.getPoints());
		}
	}

	/**
	 * Get the current game mode
	 *
	 * @return The current GameMode
	 */
	public GameMode getCurrentMode() {
		return currentMode;
	}

	/**
	 * Get the network manager instance
	 *
	 * @return The NetworkManager, or null if not in network mode
	 */
	public NetworkManager getNetworkManager() {
		return networkManager;
	}

	/**
	 * Cleanup resources when shutting down multiplayer
	 */
	public void cleanup() {
		if (networkManager != null) {
			networkManager.cleanup();
			networkManager = null;
		}
	}

	/**
	 * Get a display-friendly name for the current game mode
	 *
	 * @return String representation of the current mode
	 */
	public String getCurrentModeDisplayName() {
		switch (currentMode) {
			case LOCAL_SINGLE_PLAYER:
				return "Single Player";
			case LOCAL_MULTIPLAYER:
				return "Local Multiplayer";
			case NETWORK_HOST:
				return "Network Host";
			case NETWORK_CLIENT:
				return "Network Client";
			default:
				return "Unknown";
		}
	}

	/**
	 * Check if network is connected
	 *
	 * @return true if network connection is active
	 */
	public boolean isNetworkConnected() {
		return networkManager != null && networkManager.isConnected();
	}
}
