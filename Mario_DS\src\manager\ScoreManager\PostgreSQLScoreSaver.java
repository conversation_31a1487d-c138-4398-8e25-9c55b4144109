package manager.ScoreManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import manager.GameEngine;
import manager.GameStatus;

public class PostgreSQLScoreSaver implements GameObserver {

	public PostgreSQLScoreSaver() {
		// Constructor - database initialization happens in DatabaseConfig
	}

	@Override
	public void update(GameEngine gameEngine) {
		if (gameEngine.getGameStatus() == GameStatus.GAME_OVER
				|| gameEngine.getGameStatus() == GameStatus.MISSION_PASSED) {

			// Get scores from game engine
			int marioScore = gameEngine.getScore();
			int mario2Score = gameEngine.getScore2();
			int totalScore = marioScore + mario2Score;

			LocalDateTime now = LocalDateTime.now();

			saveScoreToDatabase(marioScore, mario2Score, totalScore, now);
		}
	}

	private void saveScoreToDatabase(int marioScore, int mario2Score, int totalScore, LocalDateTime gameDate) {
		String insertSQL = """
				INSERT INTO mario_scores (mario_score, mario2_score, total_score, game_date)
				VALUES (?, ?, ?, ?)
				""";

		try (Connection conn = DatabaseConfig.getConnection();
				PreparedStatement pstmt = conn.prepareStatement(insertSQL)) {

			pstmt.setInt(1, marioScore);
			pstmt.setInt(2, mario2Score);
			pstmt.setInt(3, totalScore);
			pstmt.setObject(4, gameDate);

			int rowsAffected = pstmt.executeUpdate();

			if (rowsAffected > 0) {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				String formattedDateTime = gameDate.format(formatter);

				System.out.println("Score saved to database successfully!");
				System.out.println("Mario: " + marioScore + " - Mario2: " + mario2Score +
						" - Total: " + totalScore + " - Date: " + formattedDateTime);
			} else {
				System.err.println("Failed to save score to database");
			}

		} catch (SQLException e) {
			System.err.println("Error saving score to database: " + e.getMessage());
			e.printStackTrace();

			// Fallback to file saving if database fails
			fallbackToFileSaving(marioScore, mario2Score, gameDate);
		}
	}

	private void fallbackToFileSaving(int marioScore, int mario2Score, LocalDateTime gameDate) {
		System.out.println("Falling back to file-based score saving...");
		ScoreSaver fileSaver = new ScoreSaver("src/media/score/score.txt");

		// Create a temporary GameEngine-like object for fallback
		// Note: This is a simplified approach - in a real scenario, you might want to
		// pass the original GameEngine or refactor the ScoreSaver to accept direct
		// parameters
		System.out.println("Fallback - Mario: " + marioScore + " - Mario2: " + mario2Score +
				" - Date: " + gameDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
	}
}
