package manager.network;

/**
 * Network message for connection management between host and client.
 * Handles connection requests, acceptances, rejections, and disconnections.
 */
public class ConnectionMessage extends NetworkMessage {
    
    private static final long serialVersionUID = 1L;
    
    private String playerName;
    private String reason; // For rejections or disconnections
    private boolean success;
    
    public ConnectionMessage(MessageType messageType, String senderId, String playerName, boolean success, String reason) {
        super(messageType, senderId);
        this.playerName = playerName;
        this.success = success;
        this.reason = reason;
    }
    
    // Constructor for connection requests
    public ConnectionMessage(String senderId, String playerName) {
        super(MessageType.CONNECTION_REQUEST, senderId);
        this.playerName = playerName;
        this.success = false;
        this.reason = null;
    }
    
    // Constructor for connection responses
    public static ConnectionMessage createAccepted(String senderId, String playerName) {
        return new ConnectionMessage(MessageType.CONNECTION_ACCEPTED, senderId, playerName, true, null);
    }
    
    public static ConnectionMessage createRejected(String senderId, String playerName, String reason) {
        return new ConnectionMessage(MessageType.CONNECTION_REJECTED, senderId, playerName, false, reason);
    }
    
    // Constructor for disconnection
    public static ConnectionMessage createDisconnect(String senderId, String reason) {
        return new ConnectionMessage(MessageType.DISCONNECT, senderId, null, false, reason);
    }
    
    public String getPlayerName() {
        return playerName;
    }
    
    public String getReason() {
        return reason;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    @Override
    public String toString() {
        return String.format("ConnectionMessage[type=%s, player=%s, success=%s, reason=%s]", 
            getMessageType(), playerName, success, reason);
    }
}
