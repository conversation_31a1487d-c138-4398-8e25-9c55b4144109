package manager.network;

/**
 * Error message for communicating network or game errors.
 * Used to inform the remote party about issues that occurred.
 */
public class ErrorMessage extends NetworkMessage {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private String errorDescription;
    private boolean fatal; // Whether this error requires disconnection
    
    public ErrorMessage(String senderId, String errorCode, String errorDescription, boolean fatal) {
        super(MessageType.ERROR, senderId);
        this.errorCode = errorCode;
        this.errorDescription = errorDescription;
        this.fatal = fatal;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getErrorDescription() {
        return errorDescription;
    }
    
    public boolean isFatal() {
        return fatal;
    }
    
    @Override
    public String toString() {
        return String.format("ErrorMessage[code=%s, description=%s, fatal=%s, sender=%s]", 
            errorCode, errorDescription, fatal, getSenderId());
    }
}
