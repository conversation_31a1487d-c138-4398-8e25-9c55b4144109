package manager.network;

/**
 * Network message containing complete game state for synchronization.
 * Sent from host to client to maintain synchronized game state.
 */
public class GameStateMessage extends NetworkMessage {
    
    private static final long serialVersionUID = 1L;
    
    private SerializableGameState gameState;
    private int frameNumber; // For ordering and detecting missed updates
    
    public GameStateMessage(String senderId, SerializableGameState gameState, int frameNumber) {
        super(MessageType.GAME_STATE_UPDATE, senderId);
        this.gameState = gameState;
        this.frameNumber = frameNumber;
    }
    
    public SerializableGameState getGameState() {
        return gameState;
    }
    
    public int getFrameNumber() {
        return frameNumber;
    }
    
    @Override
    public String toString() {
        return String.format("GameStateMessage[frame=%d, sender=%s]", 
            frameNumber, getSenderId());
    }
}
