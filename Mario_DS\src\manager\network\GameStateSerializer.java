package manager.network;

import manager.Camera;
import manager.GameEngine;
import manager.MapManager;
import model.Map;
import model.brick.Brick;
import model.enemy.Enemy;
import model.hero.Fireball;
import model.hero.Mario;
import model.hero.MarioForm;
import model.prize.BoostItem;
import model.prize.Coin;
import model.prize.Prize;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for converting between game objects and their serializable
 * representations.
 * Handles the conversion of the complete game state for network transmission.
 */
public class GameStateSerializer {

	/**
	 * Convert the current game state to a serializable format
	 */
	public static SerializableGameState serializeGameState(GameEngine gameEngine, int frameNumber) {
		SerializableGameState state = new SerializableGameState();

		// Set frame number and metadata
		state.setFrameNumber(frameNumber);
		state.setRemainingTime(gameEngine.getRemainingTime());

		// Serialize camera position
		Camera camera = gameEngine.getCamera();
		if (camera != null) {
			state.setCameraX(camera.getX());
			state.setCameraY(camera.getY());
		}

		// Serialize Mario characters
		MapManager mapManager = gameEngine.getMapManager();
		if (mapManager != null) {
			Mario mario = mapManager.getMario("mario");
			Mario mario2 = mapManager.getMario("mario2");

			if (mario != null) {
				state.setMario(serializeMario(mario));
			}
			if (mario2 != null) {
				state.setMario2(serializeMario(mario2));
			}

			// Serialize game world objects
			model.Map map = mapManager.getMap();
			if (map != null) {
				state.setCurrentMapPath(map.getPath());

				// Serialize enemies
				state.setEnemies(serializeEnemies(map.getEnemies()));

				// Serialize prizes
				state.setPrizes(serializePrizes(map.getRevealedPrizes()));

				// Serialize fireballs
				state.setFireballs(serializeFireballs(map.getFireballs()));

				// Serialize bricks (only changed ones)
				state.setBricks(serializeBricks(map.getAllBricks()));
			}
		}

		return state;
	}

	/**
	 * Convert a Mario object to its serializable representation
	 */
	private static SerializableMario serializeMario(Mario mario) {
		if (mario == null)
			return null;

		MarioForm form = mario.getMarioForm();
		boolean isSuper = form != null && form.isSuper();
		boolean isFire = form != null && form.isFire();

		return new SerializableMario(
				mario.getX(), mario.getY(),
				mario.getVelX(), mario.getVelY(),
				mario.isToRight(), mario.isJumping(), mario.isFalling(),
				mario.getRemainingLives(), mario.getCoins(), mario.getPoints(),
				mario.getInvincibilityTimer(), mario.getWhichMario(),
				isSuper, isFire);
	}

	/**
	 * Convert enemy list to serializable representation
	 */
	private static List<SerializableEnemy> serializeEnemies(List<Enemy> enemies) {
		List<SerializableEnemy> serializedEnemies = new ArrayList<>();

		if (enemies != null) {
			for (Enemy enemy : enemies) {
				// Determine direction based on velocity
				boolean toRight = enemy.getVelX() > 0;

				SerializableEnemy serializedEnemy = new SerializableEnemy(
						enemy.getClass().getSimpleName(),
						enemy.getX(), enemy.getY(),
						enemy.getVelX(), enemy.getVelY(),
						toRight, true, // Assume alive if in the list
						enemy.isJumping(), enemy.isFalling(),
						false, false // TODO: Add shell state for KoopaTroopa
				);
				serializedEnemies.add(serializedEnemy);
			}
		}

		return serializedEnemies;
	}

	/**
	 * Convert prize list to serializable representation
	 */
	private static List<SerializablePrize> serializePrizes(List<Prize> prizes) {
		List<SerializablePrize> serializedPrizes = new ArrayList<>();

		if (prizes != null) {
			for (Prize prize : prizes) {
				String prizeType = prize.getClass().getSimpleName();
				String boostType = null;

				double x = 0, y = 0;
				boolean revealed = false, acquired = false;

				if (prize instanceof Coin) {
					Coin coin = (Coin) prize;
					x = coin.getX();
					y = coin.getY();
					revealed = coin.isRevealed();
					acquired = coin.isAcquired();
				} else if (prize instanceof BoostItem) {
					BoostItem boostItem = (BoostItem) prize;
					x = boostItem.getX();
					y = boostItem.getY();
					boostType = boostItem.getClass().getSimpleName(); // Use class name as type
				}

				SerializablePrize serializedPrize = new SerializablePrize(
						prizeType, x, y, 0, 0, // No velocity for prizes
						revealed, acquired, prize.getPoint(), boostType);
				serializedPrizes.add(serializedPrize);
			}
		}

		return serializedPrizes;
	}

	/**
	 * Convert fireball list to serializable representation
	 */
	private static List<SerializableFireball> serializeFireballs(List<Fireball> fireballs) {
		List<SerializableFireball> serializedFireballs = new ArrayList<>();

		if (fireballs != null) {
			for (Fireball fireball : fireballs) {
				SerializableFireball serializedFireball = new SerializableFireball(
						fireball.getX(), fireball.getY(),
						fireball.getVelX(), fireball.getVelY(),
						fireball.isToRight(),
						fireball.getOwnerId(),
						true // Assume active if in the list
				);
				serializedFireballs.add(serializedFireball);
			}
		}

		return serializedFireballs;
	}

	/**
	 * Convert brick list to serializable representation
	 */
	private static List<SerializableBrick> serializeBricks(List<Brick> bricks) {
		List<SerializableBrick> serializedBricks = new ArrayList<>();

		if (bricks != null) {
			for (Brick brick : bricks) {
				SerializableBrick serializedBrick = new SerializableBrick(
						brick.getClass().getSimpleName(),
						brick.getX(), brick.getY(),
						brick.isBroken(),
						false, false // TODO: Add empty/revealed state for surprise bricks
				);
				serializedBricks.add(serializedBrick);
			}
		}

		return serializedBricks;
	}
}
