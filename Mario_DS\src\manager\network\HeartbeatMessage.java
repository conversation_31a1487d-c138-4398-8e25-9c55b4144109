package manager.network;

/**
 * Heartbeat message to maintain connection and detect disconnections.
 * Sent periodically between host and client to ensure connection is alive.
 */
public class HeartbeatMessage extends NetworkMessage {
    
    private static final long serialVersionUID = 1L;
    
    public HeartbeatMessage(String senderId) {
        super(MessageType.HEARTBEAT, senderId);
    }
    
    @Override
    public String toString() {
        return String.format("HeartbeatMessage[sender=%s, timestamp=%d]", 
            getSenderId(), getTimestamp());
    }
}
