package manager.network;

/**
 * Enumeration of all message types that can be sent over the network.
 * Used to identify and route different types of network messages.
 */
public enum MessageType {
    
    // Connection management messages
    CONNECTION_REQUEST,     // Client requesting to connect to host
    CONNECTION_ACCEPTED,    // Host accepting client connection
    CONNECTION_REJECTED,    // Host rejecting client connection
    DISCONNECT,            // Either party disconnecting
    HEART<PERSON><PERSON>,             // Keep-alive message
    
    // Game state messages
    GAME_STATE_UPDATE,     // Complete game state synchronization
    PLAYER_INPUT,          // Player input commands
    GAME_EVENT,            // Game events (coin collected, enemy defeated, etc.)
    
    // Game control messages
    GAME_START,            // Host starting the game
    GAME_PAUSE,            // Host pausing the game
    GAME_RESUME,           // Host resuming the game
    GAME_RESET,            // Host resetting the game
    
    // Error handling
    ERROR,                 // Error message
    SYNC_REQUEST,          // Request for game state synchronization
    SYNC_RESPONSE          // Response to sync request
}
