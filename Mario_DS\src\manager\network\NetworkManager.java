package manager.network;

import manager.ButtonAction;
import manager.MultiplayerManager;

import java.io.*;
import java.net.*;
import java.util.concurrent.*;

/**
 * Manages network communication for multiplayer gaming.
 * Handles both server (host) and client connections using TCP sockets.
 */
public class NetworkManager {

	private MultiplayerManager multiplayerManager;
	private boolean isServer;
	private boolean isConnected;

	// Network components
	private ServerSocket serverSocket;
	private Socket clientSocket;
	private ObjectOutputStream outputStream;
	private ObjectInputStream inputStream;
	private ExecutorService networkExecutor;

	// Connection management
	private volatile boolean running;
	private static final int DEFAULT_PORT = 12345;
	private static final int CONNECTION_TIMEOUT = 5000; // 5 seconds

	public NetworkManager(MultiplayerManager multiplayerManager) {
		this.multiplayerManager = multiplayerManager;
		this.isServer = false;
		this.isConnected = false;
		this.running = false;
		this.networkExecutor = Executors.newCachedThreadPool();
	}

	/**
	 * Start a server to host a multiplayer game
	 *
	 * @param port The port to listen on
	 * @return true if server started successfully
	 */
	public boolean startServer(int port) {
		try {
			System.out.println("Starting server on port " + port);
			serverSocket = new ServerSocket(port);
			serverSocket.setSoTimeout(CONNECTION_TIMEOUT);

			this.isServer = true;
			this.running = true;

			// Start server listening thread
			networkExecutor.submit(this::serverListenLoop);

			System.out.println("Server started successfully on port " + port);
			return true;

		} catch (IOException e) {
			System.err.println("Failed to start server: " + e.getMessage());
			return false;
		}
	}

	/**
	 * Connect to a remote server
	 *
	 * @param hostIP The IP address of the host
	 * @param port   The port to connect to
	 * @return true if connection was successful
	 */
	public boolean connectToServer(String hostIP, int port) {
		try {
			System.out.println("Connecting to server at " + hostIP + ":" + port);
			clientSocket = new Socket();
			clientSocket.connect(new InetSocketAddress(hostIP, port), CONNECTION_TIMEOUT);

			// Set up streams
			outputStream = new ObjectOutputStream(clientSocket.getOutputStream());
			inputStream = new ObjectInputStream(clientSocket.getInputStream());

			this.isServer = false;
			this.isConnected = true;
			this.running = true;

			// Start client listening thread
			networkExecutor.submit(this::clientListenLoop);

			// Send connection request
			ConnectionMessage connectionRequest = new ConnectionMessage("client", "Player");
			sendMessage(connectionRequest);

			System.out.println("Connected to server successfully");
			return true;

		} catch (IOException e) {
			System.err.println("Failed to connect to server: " + e.getMessage());
			return false;
		}
	}

	/**
	 * Send player input to the remote host
	 *
	 * @param action   The button action
	 * @param playerId The player ID
	 */
	public void sendPlayerInput(ButtonAction action, String playerId) {
		if (!isConnected || outputStream == null) {
			return;
		}

		try {
			PlayerInputMessage inputMessage = new PlayerInputMessage("client", action, playerId, true);
			sendMessage(inputMessage);
		} catch (Exception e) {
			System.err.println("Failed to send player input: " + e.getMessage());
		}
	}

	/**
	 * Apply game state received from the network
	 *
	 * @param gameState The game state data
	 */
	public void applyGameState(Object gameState) {
		if (gameState instanceof SerializableGameState) {
			SerializableGameState state = (SerializableGameState) gameState;
			System.out.println("Applying game state: " + state);

			// Apply the game state through the multiplayer manager
			multiplayerManager.applyReceivedGameState(state);
		}
	}

	/**
	 * Check if currently connected to a network game
	 *
	 * @return true if connected
	 */
	public boolean isConnected() {
		return isConnected;
	}

	/**
	 * Check if this instance is acting as a server
	 *
	 * @return true if server mode
	 */
	public boolean isServer() {
		return isServer;
	}

	/**
	 * Cleanup network resources
	 */
	public void cleanup() {
		System.out.println("Cleaning up network resources");
		this.running = false;
		this.isConnected = false;

		try {
			if (outputStream != null) {
				outputStream.close();
			}
			if (inputStream != null) {
				inputStream.close();
			}
			if (clientSocket != null && !clientSocket.isClosed()) {
				clientSocket.close();
			}
			if (serverSocket != null && !serverSocket.isClosed()) {
				serverSocket.close();
			}
		} catch (IOException e) {
			System.err.println("Error during cleanup: " + e.getMessage());
		}

		if (networkExecutor != null) {
			networkExecutor.shutdown();
		}

		this.isServer = false;
	}

	/**
	 * Send a network message
	 */
	private void sendMessage(NetworkMessage message) {
		if (outputStream != null && isConnected) {
			try {
				outputStream.writeObject(message);
				outputStream.flush();
			} catch (IOException e) {
				System.err.println("Failed to send message: " + e.getMessage());
				handleConnectionLost();
			}
		}
	}

	/**
	 * Server listening loop - waits for client connections
	 */
	private void serverListenLoop() {
		try {
			System.out.println("Server waiting for client connection...");
			clientSocket = serverSocket.accept();
			System.out.println("Client connected: " + clientSocket.getInetAddress());

			// Set up streams
			outputStream = new ObjectOutputStream(clientSocket.getOutputStream());
			inputStream = new ObjectInputStream(clientSocket.getInputStream());

			this.isConnected = true;

			// Start message handling loop
			handleMessages();

		} catch (SocketTimeoutException e) {
			System.out.println("Server listening timeout");
		} catch (IOException e) {
			System.err.println("Server error: " + e.getMessage());
		}
	}

	/**
	 * Client listening loop - handles incoming messages
	 */
	private void clientListenLoop() {
		handleMessages();
	}

	/**
	 * Handle incoming network messages
	 */
	private void handleMessages() {
		while (running && isConnected) {
			try {
				Object receivedObject = inputStream.readObject();
				if (receivedObject instanceof NetworkMessage) {
					processMessage((NetworkMessage) receivedObject);
				}
			} catch (IOException | ClassNotFoundException e) {
				if (running) {
					System.err.println("Error receiving message: " + e.getMessage());
					handleConnectionLost();
				}
				break;
			}
		}
	}

	/**
	 * Process received network messages
	 */
	private void processMessage(NetworkMessage message) {
		System.out.println("Received message: " + message);

		switch (message.getMessageType()) {
			case CONNECTION_REQUEST:
				if (isServer) {
					// Accept the connection
					ConnectionMessage response = ConnectionMessage.createAccepted("server", "Host");
					sendMessage(response);
				}
				break;

			case CONNECTION_ACCEPTED:
				System.out.println("Connection accepted by server");
				break;

			case PLAYER_INPUT:
				if (isServer && message instanceof PlayerInputMessage) {
					PlayerInputMessage inputMsg = (PlayerInputMessage) message;
					// Forward input to multiplayer manager
					multiplayerManager.handleInput(inputMsg.getButtonAction(), inputMsg.getPlayerId());
				}
				break;

			case GAME_STATE_UPDATE:
				if (!isServer && message instanceof GameStateMessage) {
					GameStateMessage stateMsg = (GameStateMessage) message;
					applyGameState(stateMsg.getGameState());
				}
				break;

			case DISCONNECT:
				System.out.println("Remote party disconnected");
				handleConnectionLost();
				break;

			default:
				System.out.println("Unhandled message type: " + message.getMessageType());
				break;
		}
	}

	/**
	 * Handle connection loss
	 */
	private void handleConnectionLost() {
		System.out.println("Connection lost");
		this.isConnected = false;
		// TODO: Notify multiplayer manager about connection loss
	}

	/**
	 * Send game state update to client (server only)
	 */
	public void sendGameStateUpdate(SerializableGameState gameState, int frameNumber) {
		if (isServer && isConnected) {
			GameStateMessage stateMessage = new GameStateMessage("server", gameState, frameNumber);
			sendMessage(stateMessage);
		}
	}
}
