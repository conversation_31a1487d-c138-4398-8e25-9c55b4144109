package manager.network;

import java.io.Serializable;

/**
 * Base class for all network messages exchanged between host and client.
 * All network messages must be serializable for transmission over TCP sockets.
 */
public abstract class NetworkMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private long timestamp;
    private String senderId;
    private MessageType messageType;
    
    public NetworkMessage(MessageType messageType, String senderId) {
        this.messageType = messageType;
        this.senderId = senderId;
        this.timestamp = System.currentTimeMillis();
    }
    
    public MessageType getMessageType() {
        return messageType;
    }
    
    public String getSenderId() {
        return senderId;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    @Override
    public String toString() {
        return String.format("%s[type=%s, sender=%s, timestamp=%d]", 
            getClass().getSimpleName(), messageType, senderId, timestamp);
    }
}
