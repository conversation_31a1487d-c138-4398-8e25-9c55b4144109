package manager.network;

import manager.ButtonAction;

/**
 * Network message for transmitting player input commands.
 * Sent from client to host to communicate player actions.
 */
public class PlayerInputMessage extends NetworkMessage {
    
    private static final long serialVersionUID = 1L;
    
    private ButtonAction buttonAction;
    private String playerId;
    private boolean isPressed; // true for key press, false for key release
    
    public PlayerInputMessage(String senderId, ButtonAction buttonAction, String playerId, boolean isPressed) {
        super(MessageType.PLAYER_INPUT, senderId);
        this.buttonAction = buttonAction;
        this.playerId = playerId;
        this.isPressed = isPressed;
    }
    
    public ButtonAction getButtonAction() {
        return buttonAction;
    }
    
    public String getPlayerId() {
        return playerId;
    }
    
    public boolean isPressed() {
        return isPressed;
    }
    
    @Override
    public String toString() {
        return String.format("PlayerInputMessage[action=%s, player=%s, pressed=%s, sender=%s]", 
            buttonAction, playerId, isPressed, getSenderId());
    }
}
