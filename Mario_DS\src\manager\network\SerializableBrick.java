package manager.network;

import java.io.Serializable;

/**
 * Serializable representation of brick state for network synchronization.
 */
public class SerializableBrick implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String brickType; // "OrdinaryBrick", "SurpriseBrick", etc.
    private double x;
    private double y;
    private boolean broken;
    private boolean empty; // For surprise bricks
    private boolean revealed; // For surprise bricks
    
    public SerializableBrick() {
    }
    
    public SerializableBrick(String brickType, double x, double y, boolean broken, 
                           boolean empty, boolean revealed) {
        this.brickType = brickType;
        this.x = x;
        this.y = y;
        this.broken = broken;
        this.empty = empty;
        this.revealed = revealed;
    }
    
    // Getters and setters
    public String getBrickType() { return brickType; }
    public void setBrickType(String brickType) { this.brickType = brickType; }
    
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public boolean isBroken() { return broken; }
    public void setBroken(boolean broken) { this.broken = broken; }
    
    public boolean isEmpty() { return empty; }
    public void setEmpty(boolean empty) { this.empty = empty; }
    
    public boolean isRevealed() { return revealed; }
    public void setRevealed(boolean revealed) { this.revealed = revealed; }
    
    @Override
    public String toString() {
        return String.format("SerializableBrick[type=%s, pos=(%.1f,%.1f), broken=%s]", 
            brickType, x, y, broken);
    }
}
