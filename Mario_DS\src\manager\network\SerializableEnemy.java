package manager.network;

import java.io.Serializable;

/**
 * Serializable representation of enemy state for network synchronization.
 */
public class SerializableEnemy implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String enemyType; // "Goomba", "KoopaTroopa", etc.
    private double x;
    private double y;
    private double velX;
    private double velY;
    private boolean toRight;
    private boolean alive;
    private boolean jumping;
    private boolean falling;
    
    // Enemy-specific state
    private boolean isShell; // For KoopaTroopa
    private boolean shellMoving; // For KoopaTroopa shell
    
    public SerializableEnemy() {
    }
    
    public SerializableEnemy(String enemyType, double x, double y, double velX, double velY, 
                           boolean toRight, boolean alive, boolean jumping, boolean falling,
                           boolean isShell, boolean shellMoving) {
        this.enemyType = enemyType;
        this.x = x;
        this.y = y;
        this.velX = velX;
        this.velY = velY;
        this.toRight = toRight;
        this.alive = alive;
        this.jumping = jumping;
        this.falling = falling;
        this.isShell = isShell;
        this.shellMoving = shellMoving;
    }
    
    // Getters and setters
    public String getEnemyType() { return enemyType; }
    public void setEnemyType(String enemyType) { this.enemyType = enemyType; }
    
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getVelX() { return velX; }
    public void setVelX(double velX) { this.velX = velX; }
    
    public double getVelY() { return velY; }
    public void setVelY(double velY) { this.velY = velY; }
    
    public boolean isToRight() { return toRight; }
    public void setToRight(boolean toRight) { this.toRight = toRight; }
    
    public boolean isAlive() { return alive; }
    public void setAlive(boolean alive) { this.alive = alive; }
    
    public boolean isJumping() { return jumping; }
    public void setJumping(boolean jumping) { this.jumping = jumping; }
    
    public boolean isFalling() { return falling; }
    public void setFalling(boolean falling) { this.falling = falling; }
    
    public boolean isShell() { return isShell; }
    public void setShell(boolean isShell) { this.isShell = isShell; }
    
    public boolean isShellMoving() { return shellMoving; }
    public void setShellMoving(boolean shellMoving) { this.shellMoving = shellMoving; }
    
    @Override
    public String toString() {
        return String.format("SerializableEnemy[type=%s, pos=(%.1f,%.1f), alive=%s]", 
            enemyType, x, y, alive);
    }
}
