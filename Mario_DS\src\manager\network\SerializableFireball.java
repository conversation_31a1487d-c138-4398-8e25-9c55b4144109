package manager.network;

import java.io.Serializable;

/**
 * Serializable representation of fireball state for network synchronization.
 */
public class SerializableFireball implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private double x;
    private double y;
    private double velX;
    private double velY;
    private boolean toRight;
    private String ownerId; // "mario" or "mario2"
    private boolean active;
    
    public SerializableFireball() {
    }
    
    public SerializableFireball(double x, double y, double velX, double velY, 
                              boolean toRight, String ownerId, boolean active) {
        this.x = x;
        this.y = y;
        this.velX = velX;
        this.velY = velY;
        this.toRight = toRight;
        this.ownerId = ownerId;
        this.active = active;
    }
    
    // Getters and setters
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getVelX() { return velX; }
    public void setVelX(double velX) { this.velX = velX; }
    
    public double getVelY() { return velY; }
    public void setVelY(double velY) { this.velY = velY; }
    
    public boolean isToRight() { return toRight; }
    public void setToRight(boolean toRight) { this.toRight = toRight; }
    
    public String getOwnerId() { return ownerId; }
    public void setOwnerId(String ownerId) { this.ownerId = ownerId; }
    
    public boolean isActive() { return active; }
    public void setActive(boolean active) { this.active = active; }
    
    @Override
    public String toString() {
        return String.format("SerializableFireball[pos=(%.1f,%.1f), owner=%s, active=%s]", 
            x, y, ownerId, active);
    }
}
