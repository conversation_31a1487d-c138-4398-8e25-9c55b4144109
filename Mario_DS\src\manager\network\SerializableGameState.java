package manager.network;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Serializable representation of the complete game state.
 * Contains all necessary information to synchronize the game between host and client.
 */
public class SerializableGameState implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // Player states
    private SerializableMario mario;
    private SerializableMario mario2;
    
    // Game world state
    private List<SerializableEnemy> enemies;
    private List<SerializablePrize> prizes;
    private List<SerializableFireball> fireballs;
    private List<SerializableBrick> bricks;
    
    // Game metadata
    private double remainingTime;
    private int frameNumber;
    private String currentMapPath;
    private double cameraX;
    private double cameraY;
    
    public SerializableGameState() {
        this.enemies = new ArrayList<>();
        this.prizes = new ArrayList<>();
        this.fireballs = new ArrayList<>();
        this.bricks = new ArrayList<>();
    }
    
    // <PERSON> getters and setters
    public SerializableMario getMario() {
        return mario;
    }
    
    public void setMario(SerializableMario mario) {
        this.mario = mario;
    }
    
    public SerializableMario getMario2() {
        return mario2;
    }
    
    public void setMario2(SerializableMario mario2) {
        this.mario2 = mario2;
    }
    
    // Game world getters and setters
    public List<SerializableEnemy> getEnemies() {
        return enemies;
    }
    
    public void setEnemies(List<SerializableEnemy> enemies) {
        this.enemies = enemies;
    }
    
    public List<SerializablePrize> getPrizes() {
        return prizes;
    }
    
    public void setPrizes(List<SerializablePrize> prizes) {
        this.prizes = prizes;
    }
    
    public List<SerializableFireball> getFireballs() {
        return fireballs;
    }
    
    public void setFireballs(List<SerializableFireball> fireballs) {
        this.fireballs = fireballs;
    }
    
    public List<SerializableBrick> getBricks() {
        return bricks;
    }
    
    public void setBricks(List<SerializableBrick> bricks) {
        this.bricks = bricks;
    }
    
    // Game metadata getters and setters
    public double getRemainingTime() {
        return remainingTime;
    }
    
    public void setRemainingTime(double remainingTime) {
        this.remainingTime = remainingTime;
    }
    
    public int getFrameNumber() {
        return frameNumber;
    }
    
    public void setFrameNumber(int frameNumber) {
        this.frameNumber = frameNumber;
    }
    
    public String getCurrentMapPath() {
        return currentMapPath;
    }
    
    public void setCurrentMapPath(String currentMapPath) {
        this.currentMapPath = currentMapPath;
    }
    
    public double getCameraX() {
        return cameraX;
    }
    
    public void setCameraX(double cameraX) {
        this.cameraX = cameraX;
    }
    
    public double getCameraY() {
        return cameraY;
    }
    
    public void setCameraY(double cameraY) {
        this.cameraY = cameraY;
    }
    
    @Override
    public String toString() {
        return String.format("GameState[frame=%d, time=%.1f, enemies=%d, prizes=%d, fireballs=%d]", 
            frameNumber, remainingTime, enemies.size(), prizes.size(), fireballs.size());
    }
}
