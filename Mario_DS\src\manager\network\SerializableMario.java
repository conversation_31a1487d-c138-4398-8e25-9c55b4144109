package manager.network;

import java.io.Serializable;

/**
 * Serializable representation of Mario character state for network synchronization.
 */
public class SerializableMario implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // Position and movement
    private double x;
    private double y;
    private double velX;
    private double velY;
    private boolean toRight;
    private boolean jumping;
    private boolean falling;
    
    // Game state
    private int remainingLives;
    private int coins;
    private int points;
    private double invincibilityTimer;
    private String whichMario; // "mario" or "mario2"
    
    // Mario form state
    private boolean isSuper;
    private boolean isFire;
    
    public SerializableMario() {
    }
    
    public SerializableMario(double x, double y, double velX, double velY, boolean toRight, 
                           boolean jumping, boolean falling, int remainingLives, int coins, 
                           int points, double invincibilityTimer, String whichMario, 
                           boolean isSuper, boolean isFire) {
        this.x = x;
        this.y = y;
        this.velX = velX;
        this.velY = velY;
        this.toRight = toRight;
        this.jumping = jumping;
        this.falling = falling;
        this.remainingLives = remainingLives;
        this.coins = coins;
        this.points = points;
        this.invincibilityTimer = invincibilityTimer;
        this.whichMario = whichMario;
        this.isSuper = isSuper;
        this.isFire = isFire;
    }
    
    // Position getters and setters
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getVelX() { return velX; }
    public void setVelX(double velX) { this.velX = velX; }
    
    public double getVelY() { return velY; }
    public void setVelY(double velY) { this.velY = velY; }
    
    public boolean isToRight() { return toRight; }
    public void setToRight(boolean toRight) { this.toRight = toRight; }
    
    public boolean isJumping() { return jumping; }
    public void setJumping(boolean jumping) { this.jumping = jumping; }
    
    public boolean isFalling() { return falling; }
    public void setFalling(boolean falling) { this.falling = falling; }
    
    // Game state getters and setters
    public int getRemainingLives() { return remainingLives; }
    public void setRemainingLives(int remainingLives) { this.remainingLives = remainingLives; }
    
    public int getCoins() { return coins; }
    public void setCoins(int coins) { this.coins = coins; }
    
    public int getPoints() { return points; }
    public void setPoints(int points) { this.points = points; }
    
    public double getInvincibilityTimer() { return invincibilityTimer; }
    public void setInvincibilityTimer(double invincibilityTimer) { this.invincibilityTimer = invincibilityTimer; }
    
    public String getWhichMario() { return whichMario; }
    public void setWhichMario(String whichMario) { this.whichMario = whichMario; }
    
    public boolean isSuper() { return isSuper; }
    public void setSuper(boolean isSuper) { this.isSuper = isSuper; }
    
    public boolean isFire() { return isFire; }
    public void setFire(boolean isFire) { this.isFire = isFire; }
    
    @Override
    public String toString() {
        return String.format("SerializableMario[%s: pos=(%.1f,%.1f), lives=%d, points=%d, coins=%d]", 
            whichMario, x, y, remainingLives, points, coins);
    }
}
