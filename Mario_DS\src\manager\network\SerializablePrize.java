package manager.network;

import java.io.Serializable;

/**
 * Serializable representation of prize/collectible state for network synchronization.
 */
public class SerializablePrize implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String prizeType; // "Coin", "BoostItem", etc.
    private double x;
    private double y;
    private double velX;
    private double velY;
    private boolean revealed;
    private boolean acquired;
    private int point;
    
    // BoostItem specific
    private String boostType; // "MUSHROOM", "FIRE_FLOWER", etc.
    
    public SerializablePrize() {
    }
    
    public SerializablePrize(String prizeType, double x, double y, double velX, double velY,
                           boolean revealed, boolean acquired, int point, String boostType) {
        this.prizeType = prizeType;
        this.x = x;
        this.y = y;
        this.velX = velX;
        this.velY = velY;
        this.revealed = revealed;
        this.acquired = acquired;
        this.point = point;
        this.boostType = boostType;
    }
    
    // Getters and setters
    public String getPrizeType() { return prizeType; }
    public void setPrizeType(String prizeType) { this.prizeType = prizeType; }
    
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getVelX() { return velX; }
    public void setVelX(double velX) { this.velX = velX; }
    
    public double getVelY() { return velY; }
    public void setVelY(double velY) { this.velY = velY; }
    
    public boolean isRevealed() { return revealed; }
    public void setRevealed(boolean revealed) { this.revealed = revealed; }
    
    public boolean isAcquired() { return acquired; }
    public void setAcquired(boolean acquired) { this.acquired = acquired; }
    
    public int getPoint() { return point; }
    public void setPoint(int point) { this.point = point; }
    
    public String getBoostType() { return boostType; }
    public void setBoostType(String boostType) { this.boostType = boostType; }
    
    @Override
    public String toString() {
        return String.format("SerializablePrize[type=%s, pos=(%.1f,%.1f), revealed=%s, acquired=%s]", 
            prizeType, x, y, revealed, acquired);
    }
}
