package model.hero;

import views.Animation;
import views.ImageLoader;

import java.awt.image.BufferedImage;

public class MarioForm {
	public static final int SMALL = 0, SUPER = 1, FIRE = 2;
	public String whichMario;

	private Animation animation;
	private boolean isSuper, isFire; // note: fire form has priority over super form
	private BufferedImage fireballStyle;

	public MarioForm(Animation animation, boolean isSuper, boolean isFire, String whichMario) {
		this.animation = animation;
		this.isSuper = isSuper;
		this.isFire = isFire;
		this.whichMario = whichMario;

		ImageLoader imageLoader = new ImageLoader();
		BufferedImage fireball = imageLoader.loadImage("/sprite.png");
		fireballStyle = imageLoader.getSubImage(fireball, 3, 4, 24, 24);
	}

	public BufferedImage getCurrentStyle(boolean toRight, boolean movingInX, boolean movingInY) {

		BufferedImage style;

		if (movingInY && toRight) {
			style = animation.getRightFrames()[0];
		} else if (movingInY) {
			style = animation.getLeftFrames()[0];
		} else if (movingInX) {
			style = animation.animate(5, toRight);
		} else {
			if (toRight) {
				style = animation.getRightFrames()[1];
			} else
				style = animation.getLeftFrames()[1];
		}

		return style;
	}

	public MarioForm onTouchEnemy(ImageLoader imageLoader) {

		BufferedImage[] leftFrames;
		BufferedImage[] rightFrames;

		if (whichMario == "mario") {
			leftFrames = imageLoader.getLeftFrames(MarioForm.SMALL);
			rightFrames = imageLoader.getRightFrames(MarioForm.SMALL);
		} else {
			leftFrames = imageLoader.getLeftFrames2(MarioForm.SMALL);
			rightFrames = imageLoader.getRightFrames2(MarioForm.SMALL);
		}
		Animation newAnimation = new Animation(leftFrames, rightFrames);

		return new MarioForm(newAnimation, false, false, whichMario);

	}

	public Fireball fire(boolean toRight, double x, double y) {
		if (isFire) {
			return new Fireball(x, y + 48, fireballStyle, toRight, this.whichMario);
		}
		return null;
	}

	public boolean isSuper() {
		return isSuper;
	}

	public void setSuper(boolean aSuper) {
		isSuper = aSuper;
	}

	public boolean isFire() {
		return isFire;
	}
}
