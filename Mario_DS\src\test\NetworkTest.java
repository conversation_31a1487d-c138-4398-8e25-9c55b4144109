package test;

import manager.GameEngine;
import manager.GameMode;
import manager.MultiplayerManager;
import manager.network.*;

/**
 * Simple test class to verify the network architecture implementation.
 * Tests basic message creation, serialization, and network manager functionality.
 */
public class NetworkTest {
    
    public static void main(String[] args) {
        System.out.println("=== Mario Network Architecture Test ===");
        
        // Test 1: Message Creation
        testMessageCreation();
        
        // Test 2: Game State Serialization
        testGameStateSerialization();
        
        // Test 3: Network Manager Basic Functionality
        testNetworkManager();
        
        System.out.println("=== All Tests Completed ===");
    }
    
    private static void testMessageCreation() {
        System.out.println("\n--- Test 1: Message Creation ---");
        
        try {
            // Test connection message
            ConnectionMessage connMsg = new ConnectionMessage("client", "TestPlayer");
            System.out.println("✓ Connection message created: " + connMsg);
            
            // Test player input message
            PlayerInputMessage inputMsg = new PlayerInputMessage("client", 
                manager.ButtonAction.M_JUMP, "mario", true);
            System.out.println("✓ Player input message created: " + inputMsg);
            
            // Test heartbeat message
            HeartbeatMessage heartbeat = new HeartbeatMessage("client");
            System.out.println("✓ Heartbeat message created: " + heartbeat);
            
            // Test error message
            ErrorMessage error = new ErrorMessage("server", "CONN_LOST", 
                "Connection timeout", false);
            System.out.println("✓ Error message created: " + error);
            
            System.out.println("✓ Message creation test passed");
            
        } catch (Exception e) {
            System.err.println("✗ Message creation test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testGameStateSerialization() {
        System.out.println("\n--- Test 2: Game State Serialization ---");
        
        try {
            // Create a simple game state
            SerializableGameState gameState = new SerializableGameState();
            gameState.setFrameNumber(100);
            gameState.setRemainingTime(300.5);
            gameState.setCameraX(150.0);
            gameState.setCameraY(0.0);
            gameState.setCurrentMapPath("level1.txt");
            
            // Create a Mario state
            SerializableMario mario = new SerializableMario(100, 200, 5, 0, true, 
                false, false, 3, 10, 1500, 0.0, "mario", false, false);
            gameState.setMario(mario);
            
            System.out.println("✓ Game state created: " + gameState);
            System.out.println("✓ Mario state: " + mario);
            
            // Test game state message
            GameStateMessage stateMsg = new GameStateMessage("server", gameState, 100);
            System.out.println("✓ Game state message created: " + stateMsg);
            
            System.out.println("✓ Game state serialization test passed");
            
        } catch (Exception e) {
            System.err.println("✗ Game state serialization test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testNetworkManager() {
        System.out.println("\n--- Test 3: Network Manager Basic Functionality ---");
        
        try {
            // Create a dummy game engine (we'll use null for this test)
            GameEngine dummyEngine = null;
            MultiplayerManager multiplayerManager = new MultiplayerManager(dummyEngine);
            
            // Test network manager creation
            multiplayerManager.setGameMode(GameMode.NETWORK_HOST);
            System.out.println("✓ Network manager created for host mode");
            
            // Test mode switching
            multiplayerManager.setGameMode(GameMode.NETWORK_CLIENT);
            System.out.println("✓ Switched to client mode");
            
            // Test cleanup
            multiplayerManager.cleanup();
            System.out.println("✓ Network manager cleanup completed");
            
            System.out.println("✓ Network manager basic functionality test passed");
            
        } catch (Exception e) {
            System.err.println("✗ Network manager test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
