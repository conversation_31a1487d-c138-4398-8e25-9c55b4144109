package views;

public enum MultiplayerModeSelection {
	LOCAL_MULTIPLAYER(0), NETWORK_MULTIPLAYER(1), BACK_TO_MAIN_MENU(2);

	private final int lineNumber;

	MultiplayerModeSelection(int lineNumber) {
		this.lineNumber = lineNumber;
	}

	public MultiplayerModeSelection getSelection(int number) {
		if (number == 0)
			return LOCAL_MULTIPLAYER;
		else if (number == 1)
			return NETWORK_MULTIPLAYER;
		else if (number == 2)
			return BACK_TO_MAIN_MENU;
		else
			return null;
	}

	public MultiplayerModeSelection select(boolean toUp) {
		int selection;

		if (lineNumber > -1 && lineNumber < 3) {
			selection = lineNumber - (toUp ? 1 : -1);

			if (selection == -1)
				selection = 2;
			else if (selection == 3)
				selection = 0;
			return getSelection(selection);
		}

		return null;
	}

	public int getLineNumber() {
		return lineNumber;
	}
}
