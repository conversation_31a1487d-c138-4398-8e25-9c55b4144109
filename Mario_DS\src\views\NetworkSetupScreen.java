package views;

import manager.GameEngine;
import manager.GameMode;
import manager.GameStatus;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * UI screen for setting up network multiplayer connections.
 * Allows users to host a game or connect to an existing host.
 */
public class NetworkSetupScreen extends JPanel {
    
    private GameEngine gameEngine;
    private JTextField ipAddressField;
    private JTextField portField;
    private JLabel statusLabel;
    private JButton hostButton;
    private JButton connectButton;
    private JButton backButton;
    
    private static final String DEFAULT_IP = "localhost";
    private static final String DEFAULT_PORT = "12345";
    
    public NetworkSetupScreen(GameEngine gameEngine) {
        this.gameEngine = gameEngine;
        initializeComponents();
        setupLayout();
        setupEventHandlers();
    }
    
    private void initializeComponents() {
        setBackground(Color.BLACK);
        setLayout(new GridBagLayout());
        
        // IP Address input
        ipAddressField = new JTextField(DEFAULT_IP, 15);
        ipAddressField.setFont(new Font("Arial", Font.PLAIN, 16));
        
        // Port input
        portField = new JTextField(DEFAULT_PORT, 8);
        portField.setFont(new Font("Arial", Font.PLAIN, 16));
        
        // Status label
        statusLabel = new JLabel("Enter connection details");
        statusLabel.setForeground(Color.WHITE);
        statusLabel.setFont(new Font("Arial", Font.BOLD, 14));
        statusLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        // Buttons
        hostButton = new JButton("Host Game");
        connectButton = new JButton("Connect to Host");
        backButton = new JButton("Back to Menu");
        
        styleButton(hostButton);
        styleButton(connectButton);
        styleButton(backButton);
    }
    
    private void styleButton(JButton button) {
        button.setFont(new Font("Arial", Font.BOLD, 16));
        button.setBackground(new Color(70, 130, 180));
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(200, 40));
    }
    
    private void setupLayout() {
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // Title
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        JLabel titleLabel = new JLabel("Network Multiplayer Setup");
        titleLabel.setForeground(Color.YELLOW);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        add(titleLabel, gbc);
        
        // IP Address
        gbc.gridwidth = 1; gbc.gridy = 1;
        gbc.gridx = 0;
        JLabel ipLabel = new JLabel("Host IP Address:");
        ipLabel.setForeground(Color.WHITE);
        ipLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        add(ipLabel, gbc);
        
        gbc.gridx = 1;
        add(ipAddressField, gbc);
        
        // Port
        gbc.gridy = 2;
        gbc.gridx = 0;
        JLabel portLabel = new JLabel("Port:");
        portLabel.setForeground(Color.WHITE);
        portLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        add(portLabel, gbc);
        
        gbc.gridx = 1;
        add(portField, gbc);
        
        // Status
        gbc.gridy = 3; gbc.gridx = 0; gbc.gridwidth = 2;
        add(statusLabel, gbc);
        
        // Buttons
        gbc.gridy = 4; gbc.gridwidth = 1;
        gbc.gridx = 0;
        add(hostButton, gbc);
        
        gbc.gridx = 1;
        add(connectButton, gbc);
        
        gbc.gridy = 5; gbc.gridx = 0; gbc.gridwidth = 2;
        add(backButton, gbc);
    }
    
    private void setupEventHandlers() {
        hostButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                hostGame();
            }
        });
        
        connectButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                connectToHost();
            }
        });
        
        backButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                goBackToMenu();
            }
        });
    }
    
    private void hostGame() {
        try {
            int port = Integer.parseInt(portField.getText().trim());
            
            statusLabel.setText("Starting server...");
            statusLabel.setForeground(Color.YELLOW);
            
            // Set network host mode
            gameEngine.getMultiplayerManager().setGameMode(GameMode.NETWORK_HOST);
            
            // Start hosting
            boolean success = gameEngine.getMultiplayerManager().startHosting(port);
            
            if (success) {
                statusLabel.setText("Server started! Waiting for client...");
                statusLabel.setForeground(Color.GREEN);
                
                // Disable host button, enable only back button
                hostButton.setEnabled(false);
                connectButton.setEnabled(false);
                
                // TODO: Add timeout and automatic transition to map selection
                
            } else {
                statusLabel.setText("Failed to start server. Check port number.");
                statusLabel.setForeground(Color.RED);
            }
            
        } catch (NumberFormatException ex) {
            statusLabel.setText("Invalid port number");
            statusLabel.setForeground(Color.RED);
        }
    }
    
    private void connectToHost() {
        try {
            String hostIP = ipAddressField.getText().trim();
            int port = Integer.parseInt(portField.getText().trim());
            
            if (hostIP.isEmpty()) {
                statusLabel.setText("Please enter host IP address");
                statusLabel.setForeground(Color.RED);
                return;
            }
            
            statusLabel.setText("Connecting to host...");
            statusLabel.setForeground(Color.YELLOW);
            
            // Set network client mode
            gameEngine.getMultiplayerManager().setGameMode(GameMode.NETWORK_CLIENT);
            
            // Connect to host
            boolean success = gameEngine.getMultiplayerManager().connectToHost(hostIP, port);
            
            if (success) {
                statusLabel.setText("Connected successfully!");
                statusLabel.setForeground(Color.GREEN);
                
                // Disable buttons
                hostButton.setEnabled(false);
                connectButton.setEnabled(false);
                
                // TODO: Wait for host to start game or transition to waiting screen
                
            } else {
                statusLabel.setText("Failed to connect. Check IP and port.");
                statusLabel.setForeground(Color.RED);
            }
            
        } catch (NumberFormatException ex) {
            statusLabel.setText("Invalid port number");
            statusLabel.setForeground(Color.RED);
        }
    }
    
    private void goBackToMenu() {
        // Clean up any network connections
        gameEngine.getMultiplayerManager().cleanup();
        
        // Reset to single player mode
        gameEngine.getMultiplayerManager().setGameMode(GameMode.LOCAL_SINGLE_PLAYER);
        
        // Go back to multiplayer mode selection
        gameEngine.setGameStatus(GameStatus.MULTIPLAYER_MODE_SELECTION);
    }
    
    /**
     * Reset the screen state for reuse
     */
    public void resetScreen() {
        ipAddressField.setText(DEFAULT_IP);
        portField.setText(DEFAULT_PORT);
        statusLabel.setText("Enter connection details");
        statusLabel.setForeground(Color.WHITE);
        
        hostButton.setEnabled(true);
        connectButton.setEnabled(true);
        backButton.setEnabled(true);
    }
}
